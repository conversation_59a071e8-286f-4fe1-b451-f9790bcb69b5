/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --background-color: #f4f4f4;
    --text-color: #333;
    --error-color: #e74c3c;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    background-color: var(--background-color);
    color: var(--text-color);
}

/* Accessibility Utilities */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Container */
.container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* Form Styles */
.email-form {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.hint {
    color: #666;
    font-size: 0.8em;
    margin-top: 5px;
}

textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    min-height: 150px;
    font-size: 16px;
}

select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

/* Button Styles */
.btn {
    display: inline-block;
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: var(--secondary-color);
    margin-top: 10px;
}

.btn-secondary:hover {
    background-color: #27ae60;
}

/* Loader */
.loader {
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    background-color: #ffebee;
    color: var(--error-color);
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
    text-align: center;
}

/* Enhanced Result */
#enhanced-result {
    background-color: white;
    padding: 30px;
    margin-top: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.enhanced-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.result-actions {
    display: flex;
    gap: 10px;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 30px;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    font-size: 0.9em;
    color: #666;
}

/* Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 10px;
    }

    .email-form, header, #enhanced-result, footer {
        padding: 15px;
    }
}