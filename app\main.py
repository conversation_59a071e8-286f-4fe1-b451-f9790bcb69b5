from flask import Flask
from flask_cors import CORS
import os

def create_app():
    # Use the directory of the current file as base
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    app = Flask(__name__, 
                static_folder=os.path.join(base_dir, 'static'),
                template_folder=os.path.join(base_dir, 'templates'))
    
    # Enable CORS
    CORS(app)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your_secret_key')
    
    # Import and register routes blueprint
    from .routes import main_blueprint
    app.register_blueprint(main_blueprint)
    
    return app