document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('email-form');
    const emailInput = document.getElementById('email-input');
    const enhancementType = document.getElementById('enhancement-type');
    const loader = document.getElementById('loader');
    const errorMessage = document.getElementById('error-message');
    const enhancedResult = document.getElementById('enhanced-result');
    const enhancedOutput = document.getElementById('enhanced-output');
    const copyBtn = document.getElementById('copy-btn');
    const downloadBtn = document.getElementById('download-btn');

    // Form submission handler
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Reset previous states
        loader.hidden = false;
        errorMessage.hidden = true;
        enhancedResult.hidden = true;
        errorMessage.textContent = '';

        try {
            // Simulate AI enhancement (replace with actual API call)
            const enhancedEmail = await enhanceEmail(
                emailInput.value, 
                enhancementType.value
            );

            // Display enhanced email
            enhancedOutput.textContent = enhancedEmail;
            loader.hidden = true;
            enhancedResult.hidden = false;
        } catch (error) {
            // Handle errors
            loader.hidden = true;
            errorMessage.textContent = error.message || 'Failed to enhance email';
            errorMessage.hidden = false;
        }
    });

    // Copy to clipboard functionality
    copyBtn.addEventListener('click', () => {
        navigator.clipboard.writeText(enhancedOutput.textContent)
            .then(() => {
                copyBtn.textContent = 'Copied!';
                setTimeout(() => {
                    copyBtn.textContent = 'Copy to Clipboard';
                }, 2000);
            })
            .catch(err => {
                console.error('Copy failed', err);
                alert('Failed to copy email');
            });
    });

    // Download functionality
    downloadBtn.addEventListener('click', () => {
        const blob = new Blob([enhancedOutput.textContent], { type: 'text/plain' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'enhanced-email.txt';
        link.click();
    });

    // Mock AI enhancement function (replace with actual API)
    async function enhanceEmail(emailText, enhancementType) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Basic enhancement logic (replace with real AI logic)
        switch(enhancementType) {
            case 'professional':
                return `Professional Version:\n${emailText}\n\n[Professionally enhanced email content]`;
            case 'friendly':
                return `Friendly Version:\n${emailText}\n\n[Warmly enhanced email content]`;
            case 'concise':
                return `Concise Version:\n${emailText}\n\n[Shortened and focused email]`;
            case 'detailed':
                return `Detailed Version:\n${emailText}\n\n[Expanded and elaborative email]`;
            default:
                return emailText;
        }
    }
});