import os
import anthropic

def enhance_email(email_text):
    """
    Enhance the given email text using Anthropic's AI
    
    Args:
        email_text (str): The original email text to be enhanced
    
    Returns:
        str: The enhanced email text
    """
    try:
        # Initialize Anthropic client
        client = anthropic.Anthropic(
            api_key=os.environ.get('ANTHROPIC_API_KEY')
        )
        
        # Create the prompt for email enhancement
        prompt = f"""You are an expert email editor. Please enhance the following email to:
        - Improve clarity and professionalism
        - Correct grammar and spelling
        - Optimize tone and communication
        - Ensure the core message remains intact

        Original Email:
        {email_text}

        Enhanced Email:
        """
        
        # Generate the enhanced email
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=1000,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )
        
        # Extract and return the enhanced text
        return response.content[0].text.strip()
    
    except Exception as e:
        # Handle any errors in AI enhancement
        return f"Error enhancing email: {str(e)}"