import os
from openai import OpenAI

def enhance_email(email_text):
    """
    Enhance the given email text using OpenAI's AI

    Args:
        email_text (str): The original email text to be enhanced

    Returns:
        str: The enhanced email text
    """
    try:
        # Initialize OpenAI client
        client = OpenAI(
            api_key=os.environ.get('OPENAI_API_KEY')
        )

        # Create the prompt for email enhancement
        prompt = f"""You are an expert email editor. Please enhance the following email to:
        - Improve clarity and professionalism
        - Correct grammar and spelling
        - Optimize tone and communication
        - Ensure the core message remains intact

        Original Email:
        {email_text}

        Enhanced Email:
        """

        # Generate the enhanced email
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            max_tokens=1000,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )

        # Extract and return the enhanced text
        return response.choices[0].message.content.strip()
    
    except Exception as e:
        # Handle any errors in AI enhancement
        return f"Error enhancing email: {str(e)}"