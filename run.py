from app.main import create_app
import os

def main():
    # Set Anthropic API key directly if not in environment
    if 'ANTHROPIC_API_KEY' not in os.environ:
        os.environ['ANTHROPIC_API_KEY'] = '************************************************************************************************************'
        print("Anthropic API key set from script.")
    
    # Create and configure the Flask app
    app = create_app()
    
    # Run the application
    # In production, use a production WSGI server like Gunicorn
    app.run(
        host='0.0.0.0',  # Listen on all available network interfaces
        port=5000,        # Default port, can be changed
        debug=True     # Set to False in production
    )

if __name__ == '__main__':
    main()