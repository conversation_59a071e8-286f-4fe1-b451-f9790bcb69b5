# File generated from our OpenAPI spec by Stainless.

from __future__ import annotations

from typing import List, Optional
from typing_extensions import Literal, Required, TypedDict

from .chat_completion_message_tool_call_param import ChatCompletionMessageToolCallParam

__all__ = ["ChatCompletionAssistantMessageParam", "FunctionCall"]


class FunctionCall(TypedDict, total=False):
    arguments: Required[str]
    """
    The arguments to call the function with, as generated by the model in JSON
    format. Note that the model does not always generate valid JSON, and may
    hallucinate parameters not defined by your function schema. Validate the
    arguments in your code before calling your function.
    """

    name: Required[str]
    """The name of the function to call."""


class ChatCompletionAssistantMessageParam(TypedDict, total=False):
    content: Required[Optional[str]]
    """The contents of the assistant message."""

    role: Required[Literal["assistant"]]
    """The role of the messages author, in this case `assistant`."""

    function_call: FunctionCall
    """Deprecated and replaced by `tool_calls`.

    The name and arguments of a function that should be called, as generated by the
    model.
    """

    tool_calls: List[ChatCompletionMessageToolCallParam]
    """The tool calls generated by the model, such as function calls."""
