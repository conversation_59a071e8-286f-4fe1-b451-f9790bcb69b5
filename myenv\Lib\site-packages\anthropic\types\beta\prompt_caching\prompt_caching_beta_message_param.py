# File generated from our OpenAPI spec by <PERSON>ainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union, Iterable
from typing_extensions import Literal, Required, TypedDict

from ...content_block import ContentBlock
from .prompt_caching_beta_text_block_param import PromptCaching<PERSON>etaTextBlockParam
from .prompt_caching_beta_image_block_param import Prompt<PERSON>achingBetaImageBlockParam
from .prompt_caching_beta_tool_use_block_param import PromptCachingBetaToolUseBlockParam
from .prompt_caching_beta_tool_result_block_param import PromptCachingBetaToolResultBlockParam

__all__ = ["PromptCachingBetaMessageParam"]


class PromptCachingBetaMessageParam(TypedDict, total=False):
    content: Required[
        Union[
            str,
            Iterable[
                Union[
                    PromptCachingBetaTextBlockParam,
                    PromptCachingBetaImageBlockParam,
                    PromptCachingBetaToolUseBlockParam,
                    PromptCachingBetaToolR<PERSON>ult<PERSON>lockParam,
                    ContentBlock,
                ]
            ],
        ]
    ]

    role: Required[Literal["user", "assistant"]]
